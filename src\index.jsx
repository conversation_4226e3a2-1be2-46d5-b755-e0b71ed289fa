import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

// Import React Toastify styles FIRST to allow override
import 'react-toastify/dist/ReactToastify.css'

// Import CSS chính bao gồm Tailwind và fonts
import './styles/main.css'

// Import FontAwesome configuration tối ưu
import './config/fontawesome'

// Import ToastContainer
import { ToastContainer } from 'react-toastify'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
    <ToastContainer
      position="top-right"
      autoClose={3000}
      hideProgressBar={false}
      newestOnTop={false}
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
      toastStyle={{
        fontFamily: 'BDLifelessGrotesk, sans-serif',
      }}
    />
  </React.StrictMode>
)