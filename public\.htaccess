# MIME Types for JavaScript modules
AddType application/javascript .js
AddType application/javascript .mjs
AddType text/javascript .js
AddType text/javascript .mjs

# Ensure proper MIME type for ES modules
<FilesMatch "\.(js|mjs)$">
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

# Enable CORS for modules
<FilesMatch "\.(js|mjs|css)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</FilesMatch>

# Cache control for assets
<FilesMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
</FilesMatch>

# Fallback for SPA routing
RewriteEngine On
RewriteBase /

# Handle React Router
RewriteRule ^(?!.*\.).*$ /index.html [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
