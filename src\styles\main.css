/* Fonts and custom styles */
@import './base/fonts.css';
@import './base/globals.css';
@import './components/utilities.css';

/* Tailwind */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global cursor reset - FIX TEXT CURSOR ISSUE */
*, *::before, *::after {
  cursor: default !important;
}

/* Override any Tailwind base styles that might set text cursor */
html, body, #root {
  cursor: default !important;
}

/* Proper cursor for interactive elements */
a, button, input, textarea, select, [role="button"], [onclick], .cursor-pointer {
  cursor: pointer !important;
}

/* Text cursor for input elements */
input[type="text"], input[type="email"], input[type="password"], input[type="search"],
input[type="url"], input[type="tel"], input[type="number"], textarea {
  cursor: text !important;
}

/* Specific fixes for common elements */
body, html, div, span, p, h1, h2, h3, h4, h5, h6 {
  cursor: default !important;
}

/* Hover states for interactive elements */
a:hover, button:hover, [role="button"]:hover, .cursor-pointer:hover {
  cursor: pointer !important;
}

/* Ensure SVG icons have pointer cursor */
svg {
  cursor: inherit !important;
}

/* Fix user-select that might be causing text cursor */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for text elements */
input, textarea, p, span, div[contenteditable], [contenteditable="true"] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* Debug: Force cursor for specific problematic elements */
nav, nav *, header, header *, .navbar, .navbar *,
.menu, .menu *, .navigation, .navigation * {
  cursor: default !important;
}

nav a, nav button, header a, header button,
.navbar a, .navbar button, .menu a, .menu button {
  cursor: pointer !important;
}

/* Override any motion/framer-motion cursor styles */
[data-framer-component], [data-motion-component] {
  cursor: default !important;
}

/* Override any React component cursor styles */
[data-react-component] {
  cursor: default !important;
}

/* Fix React Toastify cursor issues */
.Toastify__toast-container, .Toastify__toast-container * {
  cursor: default !important;
}

.Toastify__close-button {
  cursor: pointer !important;
}

/* Fix any global text selection issues */
body * {
  cursor: default !important;
}

body a, body button, body input, body textarea, body select,
body [role="button"], body [onclick], body .cursor-pointer {
  cursor: pointer !important;
}

body input[type="text"], body input[type="email"], body input[type="password"],
body input[type="search"], body input[type="url"], body input[type="tel"],
body input[type="number"], body textarea {
  cursor: text !important;
}

body {
  font-family: 'BDLifelessGrotesk', sans-serif;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom classes */

.bg-gradient-pink {
  background: linear-gradient(180deg, #FFF0F5 0%, #FFE6EE 100%);
}

.text-gradient {
  background: linear-gradient(90deg, #FF6B93 0%, #FF5C85 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Toast customization */
.toast-message {
  font-family: 'BDLifelessGrotesk', sans-serif;
  font-size: 14px;
}

.Toastify__toast {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.Toastify__toast-theme--light.Toastify__toast--success {
  background-color: #fff;
  color: #333;
  border-left: 4px solid #FE7CAB;
}

.Toastify__toast-theme--light.Toastify__toast--info {
  background-color: #fff;
  color: #333;
  border-left: 4px solid #73C2FF;
}

.Toastify__progress-bar--success {
  background-color: #FE7CAB;
}

/* Font weight test classes */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* FINAL CURSOR FIX - HIGHEST PRIORITY */
html *, body *, #root *, [class*=""] {
  cursor: default !important;
}

html a, html button, html input, html textarea, html select,
html [role="button"], html [onclick], html .cursor-pointer,
body a, body button, body input, body textarea, body select,
body [role="button"], body [onclick], body .cursor-pointer,
#root a, #root button, #root input, #root textarea, #root select,
#root [role="button"], #root [onclick], #root .cursor-pointer {
  cursor: pointer !important;
}

html input[type="text"], html input[type="email"], html input[type="password"],
html input[type="search"], html input[type="url"], html input[type="tel"],
html input[type="number"], html textarea,
body input[type="text"], body input[type="email"], body input[type="password"],
body input[type="search"], body input[type="url"], body input[type="tel"],
body input[type="number"], body textarea,
#root input[type="text"], #root input[type="email"], #root input[type="password"],
#root input[type="search"], #root input[type="url"], #root input[type="tel"],
#root input[type="number"], #root textarea {
  cursor: text !important;
}


