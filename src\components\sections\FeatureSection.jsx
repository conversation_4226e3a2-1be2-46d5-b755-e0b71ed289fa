import React from 'react';
import { motion } from 'motion/react';
import { Button } from '../common';
import { Link } from 'react-router-dom';
const FeatureSection = () => {
  const featureCardHover = {
    scale: 1.03,
    y: -5,
    transition: { duration: 0.3 }
  };

  return (
    <section className="bg-white py-8 sm:py-11">
      <div className="container mx-auto px-4 sm:px-6">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 items-center gap-6 md:gap-2 mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.6 }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl font-bold text-dexin-primary text-center md:text-left"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <PERSON><PERSON><PERSON><PERSON> tổ ấm thành <br className="hidden sm:block" />
            <span className="text-dexin-primary">chốn bình yên,</span> <br className="hidden sm:block" />
            góc nhỏ thành <span className="text-dexin-primary">nơi riêng</span> 🌷
          </motion.h2>

          <motion.div
            className="text-center md:text-left text-gray-600 text-base sm:text-lg md:text-xl max-w-5xl"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Không gian sống phản chiếu cảm xúc và phong cách của bạn.<br className="hidden sm:block" /> Hãy để DEXIN giúp bạn tạo nên một nơi hoàn hảo, đầy năng lượng và thư giãn.
          </motion.div>
        </motion.div>

        <div className="flex justify-center mb-8 sm:mb-12 w-full">
          <img src="/images/hometile-hero.jpg" alt="Feature Image" className="w-full h-auto sm:h-72 rounded-xl sm:rounded-2xl object-cover" />
        </div>

        <motion.div
          className="text-center mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-2xl sm:text-3xl font-bold text-[#D62261] mb-4 sm:mb-6">
            Dịch vụ từ DEXIN, dành riêng cho bạn
          </h3>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-10 md:gap-20 mb-12">
          <motion.div
            className="bg-dexin-light-20 text-dexin-primary p-6 rounded-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            whileHover={featureCardHover}
          >
            <h3 className="text-dexin-primary font-bold text-lg mb-4">Gợi ý phong cách độc quyền</h3>
            <p className="text-gray-600 text-sm mb-4">Đội ngũ tư vấn giúp bạn tìm ra phong cách phù hợp với không gian và cá tính.</p>
            <a href="#" className="text-sm font-bold hover:border-b-2 hover:border-dexin-primary">Khám phá thêm →</a>
          </motion.div>

          <motion.div
            className="bg-dexin-light-20 text-dexin-primary p-6 rounded-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={featureCardHover}
          >
            <h3 className="text-dexin-primary font-bold text-lg mb-4">Giải pháp decor không hư hại</h3>
            <p className="text-gray-600 text-sm mb-4">Sử dụng sơn và giấy dán để thảo gỗ, <br className="hidden sm:block" /> bảo toàn ngôi nhà.</p>
            <a href="#" className="text-sm font-bold hover:border-b-2 hover:border-dexin-primary">Khám phá thêm →</a>
          </motion.div>

          <motion.div
            className="bg-dexin-light-20 text-dexin-primary p-6 rounded-2xl sm:col-span-2 md:col-span-1"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            whileHover={featureCardHover}
          >
            <h3 className="text-dexin-primary font-bold text-lg mb-4">Chi phí hợp lý</h3>
            <p className="text-gray-600 text-sm mb-4">Giải pháp tối ưu cho học sinh, sinh viên và người thuê nhà tạm thời.</p>
            <a href="#" className="text-sm font-bold hover:border-b-2 hover:border-dexin-primary">Khám phá thêm →</a>
          </motion.div>
        </div>

        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-center mb-6 sm:mb-8 text-dexin-dark font-dexin">
            Làm mới không gian <span className="text-dexin-primary">chậm rãi</span> và <span className="text-dexin-primary">trải nghiệm việc</span><br className="hidden sm:block" />
            <span className="text-dexin-primary">decor theo cách chữa lành</span>
          </h2>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/ngo-loi"><Button variant="primary" size="lg" className="w-full sm:w-auto px-6 sm:px-10 md:px-20 py-3 sm:py-4 md:py-5 text-xl sm:text-2xl md:text-3xl rounded-full">
              Trải nghiệm ngay
            </Button></Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default FeatureSection;