import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@fonts': path.resolve(__dirname, './src/assets/fonts'),
      },
      extensions: ['.js', '.jsx', '.json'],
    },
    server: {
      port: env.VITE_PORT || 5173,
      open: true,
      host: true,
      strictPort: true,
      historyApiFallback: true
    },
    preview: {
      port: 4173,
      host: true,
      strictPort: true,
      cors: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    },
    build: {
      outDir: 'build',
      sourcemap: false, // Tắt sourcemap cho production để giảm kích thước
      assetsInlineLimit: 4096, // Inline assets nhỏ hơn 4KB
      // Đảm bảo base path đúng cho production
      base: '/',
      rollupOptions: {
        output: {
          // Tách code thành các chunks nhỏ hơn
          manualChunks: {
            // Vendor libraries
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'ui-vendor': ['motion', 'lucide-react'],
            'canvas-vendor': ['konva', 'react-konva'],
            'ai-vendor': ['@google/generative-ai', 'react-markdown'],
            'utils-vendor': ['jspdf', 'compressorjs', 'emoji-picker-react'],
            // FontAwesome được tách riêng
            'fontawesome-vendor': [
              '@fortawesome/fontawesome-svg-core',
              '@fortawesome/react-fontawesome',
              '@fortawesome/free-solid-svg-icons',
              '@fortawesome/free-regular-svg-icons',
              '@fortawesome/free-brands-svg-icons'
            ]
          },
          // Tối ưu tên file
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        }
      },
      // Tăng giới hạn cảnh báo chunk size
      chunkSizeWarningLimit: 1000,
      // Tối ưu CSS
      cssCodeSplit: true,
      // Minify options
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Loại bỏ console.log trong production
          drop_debugger: true
        }
      }
    },
    esbuild: {
      loader: 'jsx',
      include: /src\/.*\.jsx?$/,
      exclude: [],
    },
    optimizeDeps: {
      esbuildOptions: {
        loader: {
          '.js': 'jsx',
        },
      },
    },
    define: {
      // Chỉ expose các environment variables cần thiết để tránh security risk
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.VITE_TAILWIND_ENABLED': JSON.stringify(true),
      // Chỉ expose các VITE_ variables
      ...Object.keys(env).reduce((prev, key) => {
        if (key.startsWith('VITE_')) {
          prev[`process.env.${key}`] = JSON.stringify(env[key]);
        }
        return prev;
      }, {})
    },
    css: {
      postcss: './postcss.config.js',
      preprocessorOptions: {
        // Các tùy chọn CSS preprocessor
      },
    },
    assetsInclude: ['**/*.otf', '**/*.ttf', '**/*.woff', '**/*.woff2'],
  }
})